# 界面新闻爬虫

这是一个用于爬取界面新闻网站各个分类当天新闻的Python爬虫脚本。

## 功能特点

- 支持爬取界面新闻网站的31个分类的新闻
- 自动筛选当天发布的新闻文章
- 提取文章标题、链接、摘要、作者、发布时间、阅读量等信息
- 支持CSV和JSON两种输出格式
- 包含错误处理和重试机制
- 详细的日志记录

## 支持的新闻分类

包括但不限于：科技、地产、汽车、消费、工业、时尚、交通、医药、互联网、创投、能源、数码、教育、食品、新能源、家电、健康、酒业、物流、零售、美妆、体育、家居、餐饮、日用、企服、珠宝、腕表、智库、电厂、农业等。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
python jiemian_news_crawler.py
```

### 程序化使用

```python
from jiemian_news_crawler import JiemianNewsCrawler

# 创建爬虫实例
crawler = JiemianNewsCrawler()

# 爬取所有分类的当天新闻
articles = crawler.crawl_all_categories()

# 或者爬取特定分类
tech_articles = crawler.crawl_category_news(65, "科技")
```

## 输出文件

运行后会生成以下文件：

- `jiemian_news_YYYY-MM-DD.csv` - CSV格式的新闻数据
- `jiemian_news_YYYY-MM-DD.json` - JSON格式的新闻数据
- `jiemian_crawler.log` - 爬虫运行日志

## 数据字段说明

每篇文章包含以下字段：

- `category` - 新闻分类名称
- `category_id` - 分类ID
- `title` - 文章标题
- `link` - 文章链接
- `summary` - 文章摘要
- `author` - 作者
- `publish_time` - 发布时间
- `views` - 阅读量

## 注意事项

1. 请遵守网站的robots.txt协议和使用条款
2. 建议在请求间添加适当的延时，避免对服务器造成过大压力
3. 如果遇到反爬虫机制，可能需要调整请求头或添加代理
4. 网站结构可能会发生变化，需要相应调整解析逻辑

## 技术实现

- 使用 `requests` 库发送HTTP请求
- 使用 `BeautifulSoup` 解析HTML内容
- 使用正则表达式匹配时间格式
- 实现了重试机制和错误处理
- 支持多种输出格式

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。
