#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import json
import csv
import time
import logging
from urllib.parse import urljoin
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ArticleDetailCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def crawl_article_detail(self, article_url):
        """爬取单篇文章的详细内容"""
        try:
            logging.info(f"正在爬取文章详情: {article_url}")
            
            response = self.session.get(article_url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章详细信息
            article_detail = self.parse_article_detail(soup, article_url)
            
            if article_detail:
                logging.info(f"成功提取文章: {article_detail['title'][:50]}...")
                return article_detail
            else:
                logging.warning(f"无法解析文章内容: {article_url}")
                return None
                
        except Exception as e:
            logging.error(f"爬取文章详情失败 {article_url}: {e}")
            return None
    
    def parse_article_detail(self, soup, url):
        """解析文章详细内容"""
        try:
            article_detail = {
                'url': url,
                'title': '',
                'author': '',
                'publish_time': '',
                'source': '',
                'content': '',
                'images': []
            }
            
            # 提取标题 - 尝试多种选择器
            title_selectors = [
                'h1',
                '.article-title',
                '.title',
                'title'
            ]
            
            for selector in title_selectors:
                title_element = soup.select_one(selector)
                if title_element:
                    title_text = title_element.get_text(strip=True)
                    # 过滤掉包含网站名的标题
                    if '界面新闻' in title_text:
                        title_text = title_text.split('|')[0].strip()
                    if title_text and len(title_text) > 5:
                        article_detail['title'] = title_text
                        break
            
            # 提取作者、时间、来源信息
            # 查找包含作者信息的文本
            author_patterns = [
                r'([^·\s]+)\s*·\s*(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2})\s*来源:([^·\n]+)',
                r'([^·\s]+)\s*·\s*来源:([^·\n]+)',
                r'界面新闻记者\s*\|\s*([^·\n]+)'
            ]
            
            page_text = soup.get_text()
            for pattern in author_patterns:
                match = re.search(pattern, page_text)
                if match:
                    if len(match.groups()) >= 3:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['publish_time'] = match.group(2).strip()
                        article_detail['source'] = match.group(3).strip()
                    elif len(match.groups()) >= 2:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['source'] = match.group(2).strip()
                    else:
                        article_detail['author'] = match.group(1).strip()
                    break
            
            # 提取正文内容
            content_parts = []
            
            # 查找文章正文容器
            content_selectors = [
                '.article-content',
                '.content',
                '.article-body',
                '.post-content',
                'article',
                '.main-content'
            ]
            
            content_container = None
            for selector in content_selectors:
                container = soup.select_one(selector)
                if container:
                    content_container = container
                    break
            
            if not content_container:
                # 如果没找到特定容器，尝试查找包含大量文本的div
                all_divs = soup.find_all('div')
                max_text_length = 0
                for div in all_divs:
                    text_length = len(div.get_text(strip=True))
                    if text_length > max_text_length:
                        max_text_length = text_length
                        content_container = div
            
            if content_container:
                # 提取段落文本
                paragraphs = content_container.find_all(['p', 'div'])
                for para in paragraphs:
                    text = para.get_text(strip=True)
                    # 过滤掉太短的段落和导航文本
                    if (len(text) > 20 and 
                        not text.startswith('登录') and
                        not text.startswith('扫一扫') and
                        not text.startswith('Facebook') and
                        not text.startswith('微信') and
                        not text.startswith('版权所有') and
                        '界面新闻APP' not in text and
                        '未经正式授权严禁转载' not in text):
                        content_parts.append(text)
                
                # 如果段落提取效果不好，直接提取容器文本
                if len(content_parts) < 3:
                    full_text = content_container.get_text(strip=True)
                    # 按句号分割，过滤短句
                    sentences = [s.strip() for s in full_text.split('。') if len(s.strip()) > 20]
                    content_parts = sentences[:20]  # 限制句子数量
            
            article_detail['content'] = '\n\n'.join(content_parts)
            
            # 提取图片信息
            images = soup.find_all('img')
            for img in images:
                src = img.get('src', '')
                alt = img.get('alt', '')
                if src and 'http' in src:
                    article_detail['images'].append({
                        'src': src,
                        'alt': alt
                    })
            
            return article_detail
            
        except Exception as e:
            logging.error(f"解析文章详情失败: {e}")
            return None
    
    def crawl_articles_from_csv(self, csv_file, output_file=None):
        """从CSV文件读取文章链接并爬取详细内容"""
        try:
            articles_detail = []
            
            # 读取CSV文件
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                articles = list(reader)
            
            logging.info(f"从 {csv_file} 读取到 {len(articles)} 篇文章")
            
            for i, article in enumerate(articles, 1):
                logging.info(f"处理第 {i}/{len(articles)} 篇文章")
                
                article_url = article.get('link', '')
                if not article_url:
                    continue
                
                # 爬取详细内容
                detail = self.crawl_article_detail(article_url)
                if detail:
                    # 合并基本信息和详细内容
                    combined_article = {**article, **detail}
                    articles_detail.append(combined_article)
                
                # 避免请求过于频繁
                time.sleep(1)
            
            # 保存结果
            if not output_file:
                output_file = csv_file.replace('.csv', '_detailed.json')
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(articles_detail, f, ensure_ascii=False, indent=2)
            
            logging.info(f"详细内容已保存到 {output_file}")
            logging.info(f"成功爬取 {len(articles_detail)} 篇文章的详细内容")
            
            return articles_detail
            
        except Exception as e:
            logging.error(f"批量爬取文章详情失败: {e}")
            return []

def main():
    """主函数"""
    crawler = ArticleDetailCrawler()
    
    # 从今天的CSV文件爬取详细内容
    csv_file = "jiemian_news_2025-07-07.csv"
    
    print(f"开始爬取 {csv_file} 中文章的详细内容...")
    
    articles_detail = crawler.crawl_articles_from_csv(csv_file)
    
    print(f"\n爬取完成！成功获取 {len(articles_detail)} 篇文章的详细内容")

if __name__ == "__main__":
    main()
