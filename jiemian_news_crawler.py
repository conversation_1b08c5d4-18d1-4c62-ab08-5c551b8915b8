#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面新闻爬虫 - 爬取各分类当天新闻
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import csv
from datetime import datetime, date
import os
import logging
from urllib.parse import urljoin
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('jiemian_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class JiemianNewsCrawler:
    def __init__(self):
        self.base_url = "https://www.jiemian.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 分类信息 - 从HTML中提取的分类ID和名称
        self.categories = {
            65: "科技", 62: "地产", 51: "汽车", 31: "消费", 28: "工业",
            68: "时尚", 30: "交通", 472: "医药", 851: "互联网", 858: "创投",
            856: "能源", 853: "数码", 256: "教育", 845: "食品", 857: "新能源",
            850: "家电", 854: "健康", 676: "酒业", 841: "物流", 847: "零售",
            838: "美妆", 82: "体育", 694: "家居", 848: "餐饮", 846: "日用",
            852: "企服", 839: "珠宝", 840: "腕表", 605: "智库", 872: "电厂",
            883: "农业"
        }
        
        self.today = date.today().strftime("%Y-%m-%d")
        
    def get_page_content(self, url, max_retries=3):
        """获取页面内容"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response.text
            except requests.RequestException as e:
                logging.warning(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                else:
                    logging.error(f"获取页面最终失败: {url}")
                    return None
    
    def parse_article_info(self, article_element):
        """解析文章信息"""
        try:
            # 提取标题和链接
            title_element = article_element.find('h3') or article_element.find('a')
            if not title_element:
                return None
                
            title = title_element.get_text(strip=True)
            link = title_element.get('href') if title_element.name == 'a' else title_element.find('a').get('href')
            
            if link and not link.startswith('http'):
                link = urljoin(self.base_url, link)
            
            # 提取摘要
            summary_element = article_element.find('p') or article_element.find('div', class_='summary')
            summary = summary_element.get_text(strip=True) if summary_element else ""
            
            # 提取作者和时间
            author = ""
            publish_time = ""
            
            # 查找包含时间信息的元素
            time_elements = article_element.find_all(text=re.compile(r'\d{2}/\d{2}|\d+分钟前|今天|昨天'))
            for time_text in time_elements:
                time_text = time_text.strip()
                if re.search(r'\d{2}/\d{2}|\d+分钟前|今天|昨天', time_text):
                    publish_time = time_text
                    break
            
            # 查找作者信息
            author_elements = article_element.find_all(text=re.compile(r'[A-Za-z\u4e00-\u9fa5]{2,10}'))
            for author_text in author_elements:
                author_text = author_text.strip()
                if len(author_text) >= 2 and len(author_text) <= 10 and not re.search(r'\d|分钟|今天|昨天', author_text):
                    author = author_text
                    break
            
            # 提取阅读量
            views = ""
            views_element = article_element.find(text=re.compile(r'\d+\.?\d*[wk万]'))
            if views_element:
                views = views_element.strip()
            
            return {
                'title': title,
                'link': link,
                'summary': summary,
                'author': author,
                'publish_time': publish_time,
                'views': views
            }
        except Exception as e:
            logging.warning(f"解析文章信息失败: {e}")
            return None
    
    def is_today_article(self, publish_time):
        """判断是否为当天文章"""
        if not publish_time:
            return False
            
        # 匹配今天、分钟前、小时前等
        today_patterns = [
            r'今天',
            r'\d+分钟前',
            r'\d+小时前',
            r'刚刚'
        ]
        
        for pattern in today_patterns:
            if re.search(pattern, publish_time):
                return True
        
        # 匹配今天的日期格式 MM/DD
        today_mmdd = datetime.now().strftime("%m/%d").lstrip('0').replace('/0', '/')
        if today_mmdd in publish_time:
            return True
            
        return False
    
    def crawl_category_news(self, category_id, category_name):
        """爬取指定分类的新闻"""
        url = f"{self.base_url}/lists/{category_id}.html"
        logging.info(f"开始爬取 {category_name} 分类新闻: {url}")
        
        html_content = self.get_page_content(url)
        if not html_content:
            return []
        
        soup = BeautifulSoup(html_content, 'html.parser')
        articles = []
        
        # 查找文章列表容器
        article_containers = soup.find_all(['div', 'li'], class_=re.compile(r'news|article|item|list'))
        
        # 如果没找到特定容器，尝试查找包含链接的元素
        if not article_containers:
            article_containers = soup.find_all('a', href=re.compile(r'/article/\d+\.html'))
            
        for container in article_containers:
            article_info = self.parse_article_info(container)
            if article_info and article_info['title'] and self.is_today_article(article_info['publish_time']):
                article_info['category'] = category_name
                article_info['category_id'] = category_id
                articles.append(article_info)
        
        logging.info(f"{category_name} 分类找到 {len(articles)} 篇当天文章")
        return articles
    
    def save_to_csv(self, all_articles, filename=None):
        """保存到CSV文件"""
        if not filename:
            filename = f"jiemian_news_{self.today}.csv"
        
        fieldnames = ['category', 'category_id', 'title', 'link', 'summary', 'author', 'publish_time', 'views']
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_articles)
        
        logging.info(f"数据已保存到 {filename}")
    
    def save_to_json(self, all_articles, filename=None):
        """保存到JSON文件"""
        if not filename:
            filename = f"jiemian_news_{self.today}.json"
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(all_articles, jsonfile, ensure_ascii=False, indent=2)
        
        logging.info(f"数据已保存到 {filename}")
    
    def crawl_all_categories(self):
        """爬取所有分类的当天新闻"""
        all_articles = []
        
        for category_id, category_name in self.categories.items():
            try:
                articles = self.crawl_category_news(category_id, category_name)
                all_articles.extend(articles)
                time.sleep(1)  # 避免请求过于频繁
            except Exception as e:
                logging.error(f"爬取 {category_name} 分类失败: {e}")
                continue
        
        logging.info(f"总共爬取到 {len(all_articles)} 篇当天文章")
        
        # 保存数据
        if all_articles:
            self.save_to_csv(all_articles)
            self.save_to_json(all_articles)
        
        return all_articles

def main():
    """主函数"""
    crawler = JiemianNewsCrawler()
    
    print("开始爬取界面新闻各分类当天新闻...")
    print(f"目标日期: {crawler.today}")
    print(f"分类数量: {len(crawler.categories)}")
    
    articles = crawler.crawl_all_categories()
    
    print(f"\n爬取完成！")
    print(f"总共获取 {len(articles)} 篇当天文章")
    
    # 按分类统计
    category_stats = {}
    for article in articles:
        category = article['category']
        category_stats[category] = category_stats.get(category, 0) + 1
    
    print("\n各分类文章数量:")
    for category, count in sorted(category_stats.items()):
        print(f"  {category}: {count} 篇")

if __name__ == "__main__":
    main()
