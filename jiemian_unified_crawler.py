#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
界面新闻统一爬虫 - 支持财经分类和新闻分类的完整内容爬取
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import csv
from datetime import datetime, date
import os
import logging
from urllib.parse import urljoin
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('jiemian_unified_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class JiemianUnifiedCrawler:
    def __init__(self):
        self.base_url = "https://www.jiemian.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 财经分类
        self.finance_categories = {
            65: "科技",
            62: "地产", 
            51: "汽车",
            31: "消费",
            28: "工业",
            68: "时尚",
            30: "交通",
            472: "医药",
            851: "互联网",
            858: "创投",
            856: "能源",
            853: "数码",
            256: "教育",
            845: "食品",
            857: "新能源",
            850: "家电",
            854: "健康",
            676: "酒业",
            841: "物流",
            847: "零售",
            838: "美妆",
            82: "体育",
            694: "家居",
            848: "餐饮",
            846: "日用",
            852: "企服",
            839: "珠宝",
            840: "腕表",
            605: "智库",
            872: "电厂",
            883: "农业"
        }
        
        # 新闻分类
        self.news_categories = {
            32: "天下",
            71: "中国",
            277: "地方",  # 特殊URL格式
            8: "评论",
            154: "数据",
            50: "职场",
            422: "国是",
            63: "文娱",
            225: "影像",
            49: "营销",
            680: "大湾区",
            712: "ESG",
            877: "双碳",
            917: "长三角",
            927: "中西部"
        }

        # 商业分类 - 从商业主页面分析得出的子分类
        self.business_categories = {
            2: "商业",  # 商业主分类
            65: "科技",  # 商业下的科技（与财经重复，但归属不同主分类）
            62: "地产",  # 商业下的地产
            51: "汽车",  # 商业下的汽车
            31: "消费",  # 商业下的消费
            28: "工业",  # 商业下的工业
            68: "时尚",  # 商业下的时尚
            30: "交通",  # 商业下的交通
            858: "创业",  # 商业下的创业
            256: "教育",  # 商业下的教育
            854: "健康",  # 商业下的健康
            676: "酒业",  # 商业下的酒业
            694: "家居",  # 商业下的家居
            49: "营销",   # 商业下的营销
            # 注意：一些分类ID可能与财经分类重复，但在不同主分类下
        }
        
        # 特殊URL格式的分类
        self.special_categories = {
            277: "https://www.jiemian.com/city/main/277.html"  # 地方分类
        }
        
        self.today = date.today().strftime('%Y-%m-%d')
    
    def is_today_article(self, time_text):
        """判断文章是否为今天发布"""
        if not time_text:
            return False
        
        # 匹配各种时间格式
        today_patterns = [
            r'今天\s*\d{1,2}:\d{2}',
            r'\d+分钟前',
            r'\d+小时前',
            r'刚刚'
        ]
        
        for pattern in today_patterns:
            if re.search(pattern, time_text):
                return True
        
        # 匹配具体日期格式 MM/DD
        today_date = datetime.now()
        date_pattern = f"{today_date.month:02d}/{today_date.day:02d}"
        if date_pattern in time_text:
            return True
            
        return False
    
    def parse_article_info(self, article_element):
        """解析单篇文章信息"""
        try:
            article_info = {
                'title': '',
                'link': '',
                'summary': '',
                'author': '',
                'publish_time': '',
                'views': ''
            }
            
            # 查找标题和链接 - 从所有a标签中找到文章链接
            all_links = article_element.find_all('a')
            for link_element in all_links:
                href = link_element.get('href', '')
                if '/article/' in href and '.html' in href:
                    if href.startswith('http'):
                        article_info['link'] = href
                    else:
                        article_info['link'] = urljoin(self.base_url, href)
                    
                    # 获取标题文本
                    title_text = link_element.get_text(strip=True)
                    if title_text and len(title_text) > 5:
                        article_info['title'] = title_text
                    break
            
            # 如果没有找到标题，尝试其他方式
            if not article_info['title']:
                title_selectors = ['h3', 'h2', 'h1', '.title', '.card-list__title']
                for selector in title_selectors:
                    title_element = article_element.select_one(selector)
                    if title_element:
                        article_info['title'] = title_element.get_text(strip=True)
                        break
            
            # 查找摘要
            summary_selectors = [
                '.card-list__summary',
                '.summary',
                '.excerpt',
                '.description'
            ]
            
            for selector in summary_selectors:
                summary_element = article_element.select_one(selector)
                if summary_element:
                    article_info['summary'] = summary_element.get_text(strip=True)
                    break
            
            # 查找发布时间
            time_elements = article_element.find_all(string=re.compile(r'\d{2}/\d{2}|\d+分钟前|今天|昨天'))
            if time_elements:
                article_info['publish_time'] = time_elements[0].strip()
            
            # 查找作者
            author_elements = article_element.find_all(string=re.compile(r'[A-Za-z\u4e00-\u9fa5]{2,10}'))
            for author_text in author_elements:
                author_text = author_text.strip()
                if (len(author_text) >= 2 and len(author_text) <= 10 and 
                    not any(keyword in author_text for keyword in ['今天', '昨天', '分钟前', '小时前', '界面', '新闻'])):
                    article_info['author'] = author_text
                    break
            
            # 查找阅读量
            views_element = article_element.find(string=re.compile(r'\d+\.?\d*[wk万]'))
            if views_element:
                article_info['views'] = views_element.strip()
            
            return article_info
            
        except Exception as e:
            logging.warning(f"解析文章信息失败: {e}")
            return None
    
    def get_category_url(self, category_id):
        """获取分类URL"""
        if category_id in self.special_categories:
            return self.special_categories[category_id]
        else:
            return f"{self.base_url}/lists/{category_id}.html"
    
    def crawl_category_news(self, category_id, category_name, category_type="finance"):
        """爬取指定分类的当天新闻"""
        articles = []
        
        try:
            url = self.get_category_url(category_id)
            logging.info(f"开始爬取 {category_name} 分类新闻: {url}")
            
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找文章列表容器
            article_list_container = soup.find('ul', class_='card-list-items')
            
            if article_list_container:
                article_items = article_list_container.find_all('li', class_='card-list')
                
                for item in article_items:
                    article_info = self.parse_article_info(item)
                    if article_info and article_info['title'] and self.is_today_article(article_info['publish_time']):
                        article_info['category'] = category_name
                        article_info['category_id'] = category_id
                        article_info['category_type'] = category_type
                        articles.append(article_info)
            else:
                logging.warning(f"未找到 {category_name} 分类的文章列表容器")
            
        except Exception as e:
            logging.error(f"爬取 {category_name} 分类失败: {e}")
        
        logging.info(f"{category_name} 分类找到 {len(articles)} 篇当天文章")
        return articles

    def crawl_article_detail(self, article_url):
        """爬取单篇文章的详细内容"""
        try:
            logging.info(f"正在爬取文章详情: {article_url}")
            response = self.session.get(article_url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')
            article_detail = self.parse_article_detail(soup, article_url)
            if article_detail:
                logging.info(f"成功提取文章: {article_detail['title'][:50]}...")
                return article_detail
            else:
                logging.warning(f"无法解析文章内容: {article_url}")
                return None
        except Exception as e:
            logging.error(f"爬取文章详情失败 {article_url}: {e}")
            return None

    def parse_article_detail(self, soup, url):
        """解析文章详细内容"""
        try:
            article_detail = {
                'url': url,
                'title': '',
                'author': '',
                'publish_time': '',
                'source': '',
                'content': '',
                'images': []
            }

            # 提取标题
            title_selectors = ['h1', '.article-title', '.title', 'title']
            for selector in title_selectors:
                title_element = soup.select_one(selector)
                if title_element:
                    title_text = title_element.get_text(strip=True)
                    if '界面新闻' in title_text:
                        title_text = title_text.split('|')[0].strip()
                    if title_text and len(title_text) > 5:
                        article_detail['title'] = title_text
                        break

            # 提取作者、时间、来源信息
            author_patterns = [
                r'([^·\s]+)\s*·\s*(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2})\s*来源:([^·\n]+)',
                r'([^·\s]+)\s*·\s*来源:([^·\n]+)',
                r'界面新闻记者\s*\|\s*([^·\n]+)'
            ]

            page_text = soup.get_text()
            for pattern in author_patterns:
                match = re.search(pattern, page_text)
                if match:
                    if len(match.groups()) >= 3:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['publish_time'] = match.group(2).strip()
                        article_detail['source'] = match.group(3).strip()
                    elif len(match.groups()) >= 2:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['source'] = match.group(2).strip()
                    else:
                        article_detail['author'] = match.group(1).strip()
                    break

            # 提取正文内容
            content_parts = []
            content_selectors = ['.article-content', '.content', '.article-body', '.post-content', 'article', '.main-content']

            content_container = None
            for selector in content_selectors:
                container = soup.select_one(selector)
                if container:
                    content_container = container
                    break

            if not content_container:
                all_divs = soup.find_all('div')
                max_text_length = 0
                for div in all_divs:
                    text_length = len(div.get_text(strip=True))
                    if text_length > max_text_length:
                        max_text_length = text_length
                        content_container = div

            if content_container:
                paragraphs = content_container.find_all(['p', 'div'])
                for para in paragraphs:
                    text = para.get_text(strip=True)
                    if (len(text) > 20 and
                        not text.startswith('登录') and
                        not text.startswith('扫一扫') and
                        '界面新闻APP' not in text and
                        '未经正式授权严禁转载' not in text):
                        content_parts.append(text)

                if len(content_parts) < 3:
                    full_text = content_container.get_text(strip=True)
                    sentences = [s.strip() for s in full_text.split('。') if len(s.strip()) > 20]
                    content_parts = sentences[:20]

            article_detail['content'] = '\n\n'.join(content_parts)

            # 提取图片信息
            images = soup.find_all('img')
            for img in images:
                src = img.get('src', '')
                alt = img.get('alt', '')
                if src and 'http' in src:
                    article_detail['images'].append({'src': src, 'alt': alt})

            return article_detail

        except Exception as e:
            logging.error(f"解析文章详情失败: {e}")
            return None

    def crawl_articles_detail(self, articles):
        """批量爬取文章详细内容"""
        detailed_articles = []
        total = len(articles)

        logging.info(f"开始爬取 {total} 篇文章的详细内容")

        for i, article in enumerate(articles, 1):
            logging.info(f"处理第 {i}/{total} 篇文章")

            if article.get('link'):
                detail = self.crawl_article_detail(article['link'])
                if detail:
                    merged_article = {**article, **detail}
                    detailed_articles.append(merged_article)
                else:
                    detailed_articles.append(article)
            else:
                detailed_articles.append(article)

            time.sleep(1)

        logging.info(f"成功爬取 {len(detailed_articles)} 篇文章的详细内容")
        return detailed_articles

    def save_to_csv(self, articles, filename):
        """保存数据到CSV文件"""
        if not articles:
            return

        fieldnames = ['category_type', 'category', 'category_id', 'title', 'link', 'summary', 'author', 'publish_time', 'views']

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for article in articles:
                row = {field: article.get(field, '') for field in fieldnames}
                writer.writerow(row)

        logging.info(f"数据已保存到 {filename}")

    def save_to_json(self, articles, filename):
        """保存数据到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(articles, f, ensure_ascii=False, indent=2)

        logging.info(f"数据已保存到 {filename}")

    def crawl_all_categories(self):
        """爬取所有分类的当天新闻（财经+新闻+商业）"""
        all_articles = []

        print("正在爬取财经分类...")
        # 爬取财经分类
        for category_id, category_name in self.finance_categories.items():
            articles = self.crawl_category_news(category_id, category_name, "finance")
            all_articles.extend(articles)
            time.sleep(1)

        print("正在爬取新闻分类...")
        # 爬取新闻分类
        for category_id, category_name in self.news_categories.items():
            articles = self.crawl_category_news(category_id, category_name, "news")
            all_articles.extend(articles)
            time.sleep(1)

        print("正在爬取商业分类...")
        # 爬取商业分类
        for category_id, category_name in self.business_categories.items():
            articles = self.crawl_category_news(category_id, category_name, "business")
            all_articles.extend(articles)
            time.sleep(1)

        logging.info(f"总共爬取到 {len(all_articles)} 篇当天文章")

        # 保存基础信息
        if all_articles:
            csv_filename = f"jiemian_unified_{self.today}.csv"
            json_filename = f"jiemian_unified_{self.today}.json"

            self.save_to_csv(all_articles, csv_filename)
            self.save_to_json(all_articles, json_filename)

        # 爬取详细内容
        if all_articles:
            print("\n开始爬取文章详细内容...")
            detailed_articles = self.crawl_articles_detail(all_articles)

            if detailed_articles:
                detailed_filename = f"jiemian_unified_{self.today}_detailed.json"
                self.save_to_json(detailed_articles, detailed_filename)
                return detailed_articles

        return all_articles

def main():
    """主函数"""
    crawler = JiemianUnifiedCrawler()

    print("界面新闻统一爬虫 - 完整内容爬取")
    print("=" * 60)
    print(f"目标日期: {crawler.today}")
    print(f"财经分类数量: {len(crawler.finance_categories)}")
    print(f"新闻分类数量: {len(crawler.news_categories)}")
    print(f"商业分类数量: {len(crawler.business_categories)}")
    print(f"总分类数量: {len(crawler.finance_categories) + len(crawler.news_categories) + len(crawler.business_categories)}")
    print("正在爬取所有分类的完整内容（财经+新闻+商业）...")
    print("注意：爬取详细内容需要更多时间，请耐心等待...")

    # 爬取所有分类
    articles = crawler.crawl_all_categories()

    print(f"\n爬取完成！")
    print(f"总共获取 {len(articles)} 篇文章的完整内容")

    # 按分类类型和分类统计
    finance_stats = {}
    news_stats = {}
    business_stats = {}

    for article in articles:
        category = article['category']
        category_type = article.get('category_type', 'unknown')

        if category_type == 'finance':
            finance_stats[category] = finance_stats.get(category, 0) + 1
        elif category_type == 'news':
            news_stats[category] = news_stats.get(category, 0) + 1
        elif category_type == 'business':
            business_stats[category] = business_stats.get(category, 0) + 1

    print(f"\n财经分类文章数量 (共{sum(finance_stats.values())}篇):")
    for category, count in sorted(finance_stats.items()):
        print(f"  {category}: {count} 篇")

    print(f"\n新闻分类文章数量 (共{sum(news_stats.values())}篇):")
    for category, count in sorted(news_stats.items()):
        print(f"  {category}: {count} 篇")

    print(f"\n商业分类文章数量 (共{sum(business_stats.values())}篇):")
    for category, count in sorted(business_stats.items()):
        print(f"  {category}: {count} 篇")

    # 输出文件信息
    print(f"\n输出文件:")
    print(f"  - jiemian_unified_{crawler.today}.csv (基础信息)")
    print(f"  - jiemian_unified_{crawler.today}.json (基础信息)")
    print(f"  - jiemian_unified_{crawler.today}_detailed.json (详细内容)")

if __name__ == "__main__":
    main()

    def crawl_article_detail(self, article_url):
        """爬取单篇文章的详细内容"""
        try:
            logging.info(f"正在爬取文章详情: {article_url}")
            response = self.session.get(article_url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')
            article_detail = self.parse_article_detail(soup, article_url)
            if article_detail:
                logging.info(f"成功提取文章: {article_detail['title'][:50]}...")
                return article_detail
            else:
                logging.warning(f"无法解析文章内容: {article_url}")
                return None
        except Exception as e:
            logging.error(f"爬取文章详情失败 {article_url}: {e}")
            return None

    def parse_article_detail(self, soup, url):
        """解析文章详细内容"""
        try:
            article_detail = {
                'url': url,
                'title': '',
                'author': '',
                'publish_time': '',
                'source': '',
                'content': '',
                'images': []
            }

            # 提取标题
            title_selectors = ['h1', '.article-title', '.title', 'title']
            for selector in title_selectors:
                title_element = soup.select_one(selector)
                if title_element:
                    title_text = title_element.get_text(strip=True)
                    if '界面新闻' in title_text:
                        title_text = title_text.split('|')[0].strip()
                    if title_text and len(title_text) > 5:
                        article_detail['title'] = title_text
                        break

            # 提取作者、时间、来源信息
            author_patterns = [
                r'([^·\s]+)\s*·\s*(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2})\s*来源:([^·\n]+)',
                r'([^·\s]+)\s*·\s*来源:([^·\n]+)',
                r'界面新闻记者\s*\|\s*([^·\n]+)'
            ]

            page_text = soup.get_text()
            for pattern in author_patterns:
                match = re.search(pattern, page_text)
                if match:
                    if len(match.groups()) >= 3:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['publish_time'] = match.group(2).strip()
                        article_detail['source'] = match.group(3).strip()
                    elif len(match.groups()) >= 2:
                        article_detail['author'] = match.group(1).strip()
                        article_detail['source'] = match.group(2).strip()
                    else:
                        article_detail['author'] = match.group(1).strip()
                    break

            # 提取正文内容
            content_parts = []
            content_selectors = ['.article-content', '.content', '.article-body', '.post-content', 'article', '.main-content']

            content_container = None
            for selector in content_selectors:
                container = soup.select_one(selector)
                if container:
                    content_container = container
                    break

            if not content_container:
                all_divs = soup.find_all('div')
                max_text_length = 0
                for div in all_divs:
                    text_length = len(div.get_text(strip=True))
                    if text_length > max_text_length:
                        max_text_length = text_length
                        content_container = div

            if content_container:
                paragraphs = content_container.find_all(['p', 'div'])
                for para in paragraphs:
                    text = para.get_text(strip=True)
                    if (len(text) > 20 and
                        not text.startswith('登录') and
                        not text.startswith('扫一扫') and
                        '界面新闻APP' not in text and
                        '未经正式授权严禁转载' not in text):
                        content_parts.append(text)

                if len(content_parts) < 3:
                    full_text = content_container.get_text(strip=True)
                    sentences = [s.strip() for s in full_text.split('。') if len(s.strip()) > 20]
                    content_parts = sentences[:20]

            article_detail['content'] = '\n\n'.join(content_parts)

            # 提取图片信息
            images = soup.find_all('img')
            for img in images:
                src = img.get('src', '')
                alt = img.get('alt', '')
                if src and 'http' in src:
                    article_detail['images'].append({'src': src, 'alt': alt})

            return article_detail

        except Exception as e:
            logging.error(f"解析文章详情失败: {e}")
            return None

    def crawl_articles_detail(self, articles):
        """批量爬取文章详细内容"""
        detailed_articles = []
        total = len(articles)

        logging.info(f"开始爬取 {total} 篇文章的详细内容")

        for i, article in enumerate(articles, 1):
            logging.info(f"处理第 {i}/{total} 篇文章")

            if article.get('link'):
                detail = self.crawl_article_detail(article['link'])
                if detail:
                    merged_article = {**article, **detail}
                    detailed_articles.append(merged_article)
                else:
                    detailed_articles.append(article)
            else:
                detailed_articles.append(article)

            time.sleep(1)

        logging.info(f"成功爬取 {len(detailed_articles)} 篇文章的详细内容")
        return detailed_articles
