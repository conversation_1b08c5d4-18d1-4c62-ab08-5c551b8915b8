#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

def test_single_page():
    """测试单个页面的解析"""
    url = "https://www.jiemian.com/lists/65.html"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print(f"正在获取页面: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找文章列表容器
        print("查找文章列表容器...")
        article_list_container = soup.find('ul', class_='card-list-items')
        if not article_list_container:
            article_list_container = soup.find('ul', id='load-list')
        
        if article_list_container:
            print(f"找到文章列表容器: {article_list_container.name}")
            
            # 查找所有文章项
            article_items = article_list_container.find_all('li', class_='card-list')
            print(f"找到 {len(article_items)} 个文章项")
            
            for i, item in enumerate(article_items[:3]):  # 只测试前3个
                print(f"\n--- 文章 {i+1} ---")
                
                # 查找标题
                print("查找标题元素...")
                title_element = item.find('h3', class_='card-list__title')
                if title_element:
                    print(f"找到h3标题元素: {title_element}")
                    title_link = title_element.find('a')
                    if title_link:
                        title = title_link.get_text(strip=True)
                        link = title_link.get('href')
                        print(f"标题: {title}")
                        print(f"链接: {link}")
                    else:
                        print("h3中未找到a标签")
                        print(f"h3内容: {title_element.get_text(strip=True)}")
                else:
                    print("未找到h3.card-list__title元素")
                    # 尝试查找其他可能的标题元素
                    all_h3 = item.find_all('h3')
                    print(f"找到 {len(all_h3)} 个h3标签")
                    for j, h3 in enumerate(all_h3):
                        classes = h3.get('class', [])
                        print(f"  h3 {j+1}: class={classes}, text={h3.get_text(strip=True)[:50]}...")

                    # 尝试直接查找包含文章链接的a标签
                    article_links = item.find_all('a', href=re.compile(r'/article/\d+\.html'))
                    print(f"找到 {len(article_links)} 个文章链接")
                    for j, link in enumerate(article_links):
                        print(f"  链接 {j+1}: {link.get('href')}, text={link.get_text(strip=True)[:50]}...")

                    # 查找所有a标签
                    all_links = item.find_all('a')
                    print(f"找到 {len(all_links)} 个a标签")
                    for j, link in enumerate(all_links[:3]):  # 只显示前3个
                        href = link.get('href', '')
                        text = link.get_text(strip=True)
                        print(f"  a标签 {j+1}: href={href}, text={text[:30]}...")
                
                # 查找摘要
                summary_element = item.find('div', class_='card-list__summary')
                if summary_element:
                    summary_link = summary_element.find('a')
                    if summary_link:
                        summary_p = summary_link.find('p')
                        if summary_p:
                            summary = summary_p.get_text(strip=True)
                            print(f"摘要: {summary[:100]}...")
                        else:
                            print("摘要中未找到p标签")
                    else:
                        print("摘要中未找到链接")
                else:
                    print("未找到摘要元素")
                
                # 查找footer信息
                footer_element = item.find('div', class_='news-footer')
                if footer_element:
                    # 作者
                    author_element = footer_element.find('span', class_='news-footer__author')
                    if author_element:
                        author_links = author_element.find_all('a')
                        authors = [link.get_text(strip=True) for link in author_links]
                        print(f"作者: {' '.join(authors)}")
                    
                    # 时间
                    date_element = footer_element.find('span', class_='news-footer__date')
                    if date_element:
                        publish_time = date_element.get_text(strip=True)
                        print(f"发布时间: {publish_time}")
                        
                        # 测试日期匹配
                        is_today = check_is_today(publish_time)
                        print(f"是否为今天: {is_today}")
                    
                    # 阅读量
                    right_element = footer_element.find('span', class_='news-footer__right')
                    if right_element:
                        read_num_element = right_element.find('span', class_='news-footer__read-num')
                        if read_num_element:
                            em_element = read_num_element.find('em')
                            if em_element:
                                views = em_element.get_text(strip=True)
                                print(f"阅读量: {views}")
                else:
                    print("未找到footer元素")
        else:
            print("未找到文章列表容器")
            
            # 尝试查找其他可能的容器
            print("\n尝试查找其他容器...")
            all_uls = soup.find_all('ul')
            print(f"页面中共有 {len(all_uls)} 个ul标签")
            
            for i, ul in enumerate(all_uls[:5]):
                classes = ul.get('class', [])
                id_attr = ul.get('id', '')
                print(f"ul {i+1}: class={classes}, id={id_attr}")
                
    except Exception as e:
        print(f"错误: {e}")

def check_is_today(publish_time):
    """检查是否为今天的文章"""
    if not publish_time:
        return False
        
    # 匹配今天、分钟前、小时前等
    today_patterns = [
        r'今天',
        r'\d+分钟前',
        r'\d+小时前',
        r'刚刚'
    ]
    
    for pattern in today_patterns:
        if re.search(pattern, publish_time):
            return True
    
    # 匹配今天的日期格式 MM/DD
    from datetime import datetime
    today_mmdd = datetime.now().strftime("%m/%d").lstrip('0').replace('/0', '/')
    if today_mmdd in publish_time:
        return True
        
    return False

if __name__ == "__main__":
    test_single_page()
